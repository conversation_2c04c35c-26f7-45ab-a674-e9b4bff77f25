# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""Auto-generated tools and toolsets for Google APIs.

These tools and toolsets are auto-generated based on the API specifications
provided by the Google API Discovery API.
"""

from .google_api_tool import GoogleApiTool
from .google_api_toolset import GoogleApiToolset
from .google_api_toolsets import BigQueryToolset
from .google_api_toolsets import CalendarToolset
from .google_api_toolsets import DocsToolset
from .google_api_toolsets import GmailToolset
from .google_api_toolsets import SheetsToolset
from .google_api_toolsets import SlidesToolset
from .google_api_toolsets import YoutubeToolset

__all__ = [
    'BigQueryToolset',
    'CalendarToolset',
    'GmailToolset',
    'YoutubeToolset',
    'SlidesToolset',
    'SheetsToolset',
    'DocsToolset',
    'GoogleApiToolset',
    'GoogleApiTool',
]
