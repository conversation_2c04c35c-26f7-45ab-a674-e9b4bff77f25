---
name: Update A2A Schema from Specification
on:
  repository_dispatch:
    types: [a2a_json_update]
  workflow_dispatch:
jobs:
  generate_and_pr:
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.10'
      - name: Install uv
        uses: astral-sh/setup-uv@v6
      - name: Configure uv shell
        run: echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Install dependencies (datamodel-code-generator)
        run: uv sync
      - name: Define output file variable
        id: vars
        run: |
          GENERATED_FILE="./src/a2a/types.py"
          echo "GENERATED_FILE=$GENERATED_FILE" >> "$GITHUB_OUTPUT"
      - name: Generate types from schema
        run: |
          chmod +x scripts/generate_types.sh
          ./scripts/generate_types.sh "${{ steps.vars.outputs.GENERATED_FILE }}"
      - name: Install Buf
        uses: bufbuild/buf-setup-action@v1
      - name: Run buf generate
        run: |
          set -euo pipefail  # Exit immediately if a command exits with a non-zero status
          echo "Running buf generate..."
          buf generate
          uv run scripts/grpc_gen_post_processor.py
          echo "Buf generate finished."
      - name: Create Pull Request with Updates
        uses: peter-evans/create-pull-request@v6
        with:
          token: ${{ secrets.A2A_BOT_PAT }}
          committer: a2a-bot <<EMAIL>>
          author: a2a-bot <<EMAIL>>
          commit-message: 'feat: Update A2A types from specification 🤖'
          title: 'feat: Update A2A types from specification 🤖'
          body: |
            This PR updates `src/a2a/types.py` based on the latest `specification/json/a2a.json` from [a2aproject/A2A](https://github.com/a2aproject/A2A/commit/${{ github.event.client_payload.sha }}).
          branch: auto-update-a2a-types-${{ github.event.client_payload.sha }}
          base: main
          labels: |
            automated
            dependencies
          add-paths: |-
            ${{ steps.vars.outputs.GENERATED_FILE }}
            src/a2a/grpc/
