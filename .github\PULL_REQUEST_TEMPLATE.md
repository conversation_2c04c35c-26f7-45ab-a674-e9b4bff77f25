# Description

Thank you for opening a Pull Request!
Before submitting your PR, there are a few things you can do to make sure it goes smoothly:

- [ ] Follow the [`CONTRIBUTING` Guide](https://github.com/a2aproject/a2a-python/blob/main/CONTRIBUTING.md).
- [ ] Make your Pull Request title in the <https://www.conventionalcommits.org/> specification.
  - Important Prefixes for [release-please](https://github.com/googleapis/release-please):
    - `fix:` which represents bug fixes, and correlates to a [SemVer](https://semver.org/) patch.
    - `feat:` represents a new feature, and correlates to a SemVer minor.
    - `feat!:`, or `fix!:`, `refactor!:`, etc., which represent a breaking change (indicated by the `!`) and will result in a SemVer major.
- [ ] Ensure the tests and linter pass (Run `nox -s format` from the repository root to format)
- [ ] Appropriate docs were updated (if necessary)

Fixes #<issue_number_goes_here> 🦕
