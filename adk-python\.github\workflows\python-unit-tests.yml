# Copyright 2025 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

name: Python Unit Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10", "3.11"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@v6

    - name: Install dependencies
      run: |
        uv venv .venv
        source .venv/bin/activate
        uv sync --extra test --extra eval --extra a2a

    - name: Run unit tests with pytest
      run: |
        source .venv/bin/activate
        if [[ "${{ matrix.python-version }}" == "3.9" ]]; then
          pytest tests/unittests \
            --ignore=tests/unittests/a2a \
            --ignore=tests/unittests/tools/mcp_tool \
            --ignore=tests/unittests/artifacts/test_artifact_service.py \
            --ignore=tests/unittests/tools/google_api_tool/test_googleapi_to_openapi_converter.py
        else
          pytest tests/unittests \
            --ignore=tests/unittests/artifacts/test_artifact_service.py \
            --ignore=tests/unittests/tools/google_api_tool/test_googleapi_to_openapi_converter.py
        fi