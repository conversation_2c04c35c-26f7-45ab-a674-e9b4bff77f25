import logging
import uuid
import json
from typing import List, Dict, Any

from collections.abc import AsyncGenerator

from a2a.server.agent_execution import <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestContext
from a2a.server.events import EventQueue
from a2a.types import (
    AgentCard,
    TaskState,
    TaskStatus,
    TaskStatusUpdateEvent,
)

from a2a.utils import new_agent_text_message
from google.adk.agents import Agent
from google.adk.events import Event
from google.adk.runners import Runner
from google.adk.sessions import Session as ADKSession
from google.genai import types as adk_types

import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OcrExecutor(AgentExecutor):

    def __init__(self, agent: Agent, agent_card: AgentCard, runner: Runner):
        self.agent = agent
        self.agent_card = agent_card
        self.runner = runner

        self.session_service = runner.session_service
        self.artifact_service = runner.artifact_service
        logger.info(
            f"ADK Runner accepted for app '{self.runner.app_name}' for agent '{self.agent.name}'"
        )


    async def execute(self, context: RequestContext, event_queue: EventQueue) -> None:
        try:
            # Step 1: Prepare the user's input for execution with the LLM
            user_input = self._prepare_input(context)

            # Step 2: Prepare all session and context related data
            user_id, session_id = self._get_session_identifiers(context)
            await self._ensure_adk_session(user_id, session_id)

            # Step 3: Send the input to the LLM and loop until a final response is received
            final_message_text = await self._run_agent_and_get_response(
                user_input, user_id, session_id
            )

            # Step 4: Send the response back to the client
            self._send_response(event_queue, context, final_message_text)

        except Exception as e:
            self._handle_error(event_queue, context, e)


    async def cancel(self, context: RequestContext, event_queue: EventQueue) -> None:
        ...


    def _prepare_input(self, context: RequestContext) -> str:
        """Prepare and validate user input for image array processing."""
        user_input = context.get_user_input()
        if not user_input:
            logger.warning(
                f"No user input found for {self.agent.name}; using default message."
            )
            user_input = "No images provided for OCR processing"

        # Check if input contains image array information
        try:
            # Try to parse as JSON to see if it's an image array
            parsed_input = json.loads(user_input)
            if isinstance(parsed_input, list):
                logger.info(f"{self.agent.name} processing {len(parsed_input)} images for OCR")
                # Get structured data and convert to instruction
                structured_data = self._format_image_array_input(parsed_input)
                return self._create_agent_instruction(structured_data)
            else:
                logger.info(f"{self.agent.name} processing single input: '{user_input[:100]}'")
                return user_input
        except (json.JSONDecodeError, TypeError):
            # Not JSON, treat as regular text input
            logger.info(f"{self.agent.name} processing text input: '{user_input[:100]}'")
            return user_input

    def _format_image_array_input(self, image_array: List[Any]) -> Dict[str, Any]:
        """Format image array input for processing and return structured data."""
        formatted_images = []
        for i, image_data in enumerate(image_array):
            # Generate unique ID for each image
            image_id = str(uuid.uuid4())

            # Extract image information
            if isinstance(image_data, dict):
                image_path = image_data.get('path', image_data.get('file', f'image_{i}'))
                image_name = image_data.get('name', f'Image_{i+1}')
                image_description = image_data.get('description', 'Image for OCR processing')
            else:
                # Assume it's a file path or string
                image_path = str(image_data)
                image_name = f'Image_{i+1}'
                image_description = 'Image for OCR processing'

            formatted_images.append({
                'id': image_id,
                'path': image_path,
                'name': image_name,
                'description': image_description,
                'index': i
            })

        # Return structured data instead of instruction string
        return {
            'task_type': 'batch_ocr_processing',
            'image_count': len(formatted_images),
            'images': formatted_images,
            'processing_requirements': {
                'extract_text': True,
                'analyze_content': True,
                'generate_metadata': True,
                'return_structured_results': True
            },
            'output_schema': {
                'id': 'string - The unique ID assigned to the image',
                'original_name': 'string - The original name provided',
                'updated_name': 'string - New descriptive name based on OCR content and analysis',
                'original_description': 'string - The original description provided',
                'updated_description': 'string - New detailed description based on analysis',
                'ocr_text': 'string - All extracted text content from the image',
                'analysis': 'string - Detailed visual content analysis including layout, objects, and context'
            }
        }

    def _create_agent_instruction(self, structured_data: Dict[str, Any]) -> str:
        """Create agent instruction from structured data."""
        images = structured_data['images']
        image_count = structured_data['image_count']

        instruction = f"""
        Process the following {image_count} images for OCR analysis:

        {json.dumps(images, indent=2)}

        For each image:
        1. Use the OCR tools to extract all text content from the image
        2. Analyze the visual content, layout, and context
        3. Generate an updated name based on the extracted content and visual analysis
        4. Generate an updated description based on the comprehensive analysis
        5. Return structured results with the assigned ID

        IMPORTANT: Return the results as a valid JSON array where each element contains:
        {{
            "id": "The unique ID assigned to the image",
            "original_name": "The original name provided",
            "updated_name": "New descriptive name based on OCR content and analysis",
            "original_description": "The original description provided",
            "updated_description": "New detailed description based on analysis",
            "ocr_text": "All extracted text content from the image",
            "analysis": "Detailed visual content analysis including layout, objects, and context"
        }}

        Process each image thoroughly and provide comprehensive results.
        """

        return instruction

    def _get_session_identifiers(self, context: RequestContext) -> tuple[str, str]:
        """Get user_id and session_id for ADK session management."""
        user_id = "a2a_user_ocr"
        session_id = context.task_id or str(uuid.uuid4())
        return user_id, session_id

    async def _ensure_adk_session(self, user_id: str, session_id: str) -> None:
        """Create or retrieve ADK session."""
        adk_session: ADKSession | None = await self.session_service.get_session(
            app_name=self.runner.app_name, user_id=user_id, session_id=session_id
        )
        
        if not adk_session:
            await self.session_service.create_session(
                app_name=self.runner.app_name,
                user_id=user_id,
                session_id=session_id,
                state={},
            )

        logger.info(f"Created new ADK session: {session_id} for {self.agent.name}")

    async def _run_agent_and_get_response(
        self, user_input: str, user_id: str, session_id: str
    ) -> str:
        """Run the ADK agent and extract the final response."""
        request_content = adk_types.Content(
            role="user", parts=[adk_types.Part(text=user_input)]
        )

        logger.debug(f"Running ADK agent {self.agent.name} with session {session_id}")
        events_async: AsyncGenerator[Event, None] = self.runner.run_async(
            user_id=user_id, session_id=session_id, new_message=request_content
        )

        final_message_text = "(No OCR results found)"

        async for event in events_async:
            if (
                event.is_final_response()
                and event.content
                and event.content.role == "model"
            ):
                if event.content.parts and event.content.parts[0].text:
                    final_message_text = event.content.parts[0].text
                    logger.info(
                        f"{self.agent.name} final response: '{final_message_text[:200]}'{'...' if len(final_message_text) > 200 else ''}"
                    )
                    break
                else:
                    logger.warning(
                        f"{self.agent.name} received final event but no text in first part: {event.content.parts}"
                    )
            elif event.is_final_response():
                logger.warning(
                    f"{self.agent.name} received final event without model content: {event}"
                )

        # Process and format the response for image array results
        return self._format_ocr_response(final_message_text)

    def _format_ocr_response(self, response_text: str) -> str:
        """Format the OCR response to ensure proper structure."""
        try:
            # Try to parse as JSON to validate structure
            parsed_response = json.loads(response_text)
            if isinstance(parsed_response, list):
                # Validate and enhance each image result
                formatted_results = []
                for i, result in enumerate(parsed_response):
                    if not isinstance(result, dict):
                        # Create a default structure if not a dict
                        result = {
                            'id': str(uuid.uuid4()),
                            'original_name': f'Image_{i+1}',
                            'updated_name': f'Processed_Image_{i+1}',
                            'original_description': 'Image for processing',
                            'updated_description': 'Processed image result',
                            'ocr_text': str(result) if result else 'No text extracted',
                            'analysis': 'Basic processing completed'
                        }
                    else:
                        # Ensure all required fields are present with meaningful defaults
                        required_fields = {
                            'id': str(uuid.uuid4()),
                            'original_name': f'Image_{i+1}',
                            'updated_name': 'Processed Image',
                            'original_description': 'Image for processing',
                            'updated_description': 'OCR processed image',
                            'ocr_text': 'No text extracted',
                            'analysis': 'Visual analysis completed'
                        }

                        for field, default_value in required_fields.items():
                            if field not in result or not result[field]:
                                result[field] = default_value

                    formatted_results.append(result)

                # Return formatted JSON with proper structure
                return json.dumps({
                    'status': 'success',
                    'processed_images': len(formatted_results),
                    'results': formatted_results
                }, indent=2)
            else:
                # Single result, wrap in array format
                return json.dumps({
                    'status': 'success',
                    'processed_images': 1,
                    'results': [{
                        'id': str(uuid.uuid4()),
                        'original_name': 'Single Image',
                        'updated_name': 'Processed Image',
                        'original_description': 'Single image input',
                        'updated_description': 'OCR processed single image',
                        'ocr_text': str(parsed_response),
                        'analysis': 'Single image processing completed'
                    }]
                }, indent=2)
        except (json.JSONDecodeError, TypeError):
            # Not JSON, wrap as text result
            return json.dumps({
                'status': 'success',
                'processed_images': 1,
                'results': [{
                    'id': str(uuid.uuid4()),
                    'original_name': 'Text Input',
                    'updated_name': 'Processed Text',
                    'original_description': 'Text input for processing',
                    'updated_description': 'Text processing completed',
                    'ocr_text': response_text,
                    'analysis': 'Text-based response processing'
                }]
            }, indent=2)
        
    def _send_response(
    self, event_queue: EventQueue, context: RequestContext, message_text: str
    ) -> None:
        """Send the response back via the event queue."""
        logger.info(f"Sending OCR processing response for task {context.task_id}")
        event_queue.enqueue_event(
            new_agent_text_message(
                text=message_text,
                context_id=context.context_id,
                task_id=context.task_id,
            )
        )

    def _handle_error(
        self,
        event_queue: EventQueue,
        context: RequestContext,
        error: Exception
    ) -> None:
        """Handle errors and send error response."""
        logger.error(
            f"Error executing OCR processing in {self.agent.name}: {str(error)}",
            exc_info=True,
        )
        error_message_text = f"Error processing images with OCR: {str(error)}"
        event_queue.enqueue_event(
            new_agent_text_message(
                text=error_message_text,
                context_id=context.context_id,
                task_id=context.task_id,
            )
        )

