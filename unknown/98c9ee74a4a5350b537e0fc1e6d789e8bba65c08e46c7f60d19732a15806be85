[project]
name = "a2a-sdk"
dynamic = ["version"]
description = "A2A Python SDK"
readme = "README.md"
license = "Apache-2.0"
authors = [{ name = "Google LLC", email = "<EMAIL>" }]
requires-python = ">=3.10"
keywords = ["A2A", "A2A SDK", "A2A Protocol", "Agent2Agent", "Agent 2 Agent"]
dependencies = [
  "fastapi>=0.115.2",
  "httpx>=0.28.1",
  "httpx-sse>=0.4.0",
  "google-api-core>=1.26.0",
  "opentelemetry-api>=1.33.0",
  "opentelemetry-sdk>=1.33.0",
  "pydantic>=2.11.3",
  "sse-starlette",
  "starlette",
  "grpcio>=1.60",
  "grpcio-tools>=1.60",
  "grpcio_reflection>=1.7.0",
  "protobuf==5.29.5",
]

classifiers = [
  "Intended Audience :: Developers",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
  "Operating System :: OS Independent",
  "Topic :: Software Development :: Libraries :: Python Modules",
  "License :: OSI Approved :: Apache Software License",
]

[project.optional-dependencies]
postgresql = ["sqlalchemy[asyncio,postgresql-asyncpg]>=2.0.0"]
mysql = ["sqlalchemy[asyncio,aiomysql]>=2.0.0"]
sqlite = ["sqlalchemy[asyncio,aiosqlite]>=2.0.0"]
sql = ["sqlalchemy[asyncio,postgresql-asyncpg,aiomysql,aiosqlite]>=2.0.0"]

[project.urls]
homepage = "https://a2aproject.github.io/A2A/"
repository = "https://github.com/a2aproject/a2a-python"
changelog = "https://github.com/a2aproject/a2a-python/blob/main/CHANGELOG.md"
documentation = "https://a2aproject.github.io/A2A/sdk/python/"

[tool.hatch.build.targets.wheel]
packages = ["src/a2a"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_functions = "test_*"
addopts = "-ra --strict-markers"
markers = [
  "asyncio: mark a test as a coroutine that should be run by pytest-asyncio",
]

[tool.pytest-asyncio]
mode = "strict"

[build-system]
requires = ["hatchling", "uv-dynamic-versioning"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.hatch.build.targets.sdist]
exclude = ["tests/"]

[tool.uv-dynamic-versioning]
vcs = "git"
style = "pep440"

[dependency-groups]
dev = [
  "datamodel-code-generator>=0.30.0",
  "mypy>=1.15.0",
  "pytest>=8.3.5",
  "pytest-asyncio>=0.26.0",
  "pytest-cov>=6.1.1",
  "pytest-mock>=3.14.0",
  "respx>=0.20.2",
  "ruff>=0.11.6",
  "uv-dynamic-versioning>=0.8.2",
  "types-protobuf",
  "types-requests",
  "pre-commit",
]

[[tool.uv.index]]
name = "testpypi"
url = "https://test.pypi.org/simple/"
publish-url = "https://test.pypi.org/legacy/"
explicit = true

[tool.pyright]
include = ["src"]
exclude = [
  "**/__pycache__",
  "**/dist",
  "**/build",
  "**/node_modules",
  "**/venv",
  "**/.venv",
  "src/a2a/grpc/",
]
reportMissingImports = "none"
reportMissingModuleSource = "none"
