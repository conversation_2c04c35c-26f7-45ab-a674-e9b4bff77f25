---
name: Run Unit Tests
on:
  pull_request:
    branches: [main]
permissions:
  contents: read
jobs:
  test:
    name: Test with Python ${{ matrix.python-version }}
    runs-on: ubuntu-latest

    if: github.repository == 'a2aproject/a2a-python'
    services:
      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_USER: postgres
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: a2a_test
        ports:
          - 5432:5432

    strategy:
      matrix:
        python-version: ['3.10', '3.13']
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Set postgres for tests
        run: |
          sudo apt-get update && sudo apt-get install -y postgresql-client
          PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -d a2a_test -f ${{ github.workspace }}/docker/postgres/init.sql
          export POSTGRES_TEST_DSN="postgresql+asyncpg://postgres:postgres@localhost:5432/a2a_test"

      - name: Install uv
        uses: astral-sh/setup-uv@v6
      - name: Add uv to PATH
        run: |
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
      - name: Install dependencies
        run: uv sync --dev --extra sql
      - name: Run tests and check coverage
        run: uv run pytest --cov=a2a --cov-report=xml --cov-fail-under=90
      - name: Show coverage summary in log
        run: uv run coverage report
