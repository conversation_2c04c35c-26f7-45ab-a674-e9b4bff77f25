# Template taken from https://github.com/v8/v8/blob/master/.git-blame-ignore-revs.
#
# This file contains a list of git hashes of revisions to be ignored by git blame. These
# revisions are considered "unimportant" in that they are unlikely to be what you are
# interested in when blaming. Most of these will probably be commits related to linting
# and code formatting.
#
# Instructions:
# - Only large (generally automated) reformatting or renaming CLs should be
#   added to this list. Do not put things here just because you feel they are
#   trivial or unimportant. If in doubt, do not put it on this list.
# - Precede each revision with a comment containing the PR title and number.
#   For bulk work over many commits, place all commits in a block with a single
#   comment at the top describing the work done in those commits.
# - Only put full 40-character hashes on this list (not short hashes or any
#   other revision reference).
# - Append to the bottom of the file (revisions should be in chronological order
#   from oldest to newest).
# - Because you must use a hash, you need to append to this list in a follow-up
#   PR to the actual reformatting PR that you are trying to ignore.
193693836e1ed8cd361e139668323d2e267a9eaa
