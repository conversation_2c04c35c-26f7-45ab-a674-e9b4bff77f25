{"version": "0.2.0", "configurations": [{"name": "Debug HelloWorld Agent", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/examples/helloworld/__main__.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}, "cwd": "${workspaceFolder}/examples/helloworld", "args": ["--host", "localhost", "--port", "9999"]}, {"name": "Debug Currency Agent", "type": "debugpy", "request": "launch", "program": "${workspaceFolder}/examples/langgraph/__main__.py", "console": "integratedTerminal", "justMyCode": false, "env": {"PYTHONPATH": "${workspaceFolder}"}, "cwd": "${workspaceFolder}/examples/langgraph", "args": ["--host", "localhost", "--port", "10000"]}, {"name": "Pytest All", "type": "debugpy", "request": "launch", "module": "pytest", "args": ["-v", "-s"], "console": "integratedTerminal", "justMyCode": true}]}